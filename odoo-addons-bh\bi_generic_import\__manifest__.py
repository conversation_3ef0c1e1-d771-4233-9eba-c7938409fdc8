# -*- coding: utf-8 -*-
# Part of BrowseInfo. See LICENSE file for full copyright and licensing details.

{
    'name': 'Odoo all import for Sales, Purchase, Invoice, Inventory, Pricelist, BOM, Payment, Journal Entry, Picking, Product, Customer.',
    'version': '********',
    'sequence': 4,
    'summary': 'Odoo import Data Import All in one import Invoice import Sales import Inventory import Purchase import stock inventory import Picking import Product image import Customer import serial import journal entry import payment',
    'price': 84,
    'currency': 'EUR',
    'category': 'Extra Tools',
    'description': """
    

	BrowseInfo developed a new odoo/OpenERP module apps
	This module use for import bulk bom from Excel file. Import bill of material from CSV or Excel file.
    Import bom lines, Import multiple bom lines, Import bill of material using excel, Import BOM from csv.BOM Import, Add BOM from Excel.Add bom form csv.Add CSV file.Import bom data. Import excel file
    bill of material import, bom line import from excel, import bill of material lines.
    import bill of materials
    import bills of materials
    Odoo all import for Sales, Purchase, Invoice, Inventory, BOM, Customer/Supplier Payment, Bank Statement, Journal Entry, Picking, Product, Customer.
    Odoo all import
    Odoo connector
    Odoo bridge
    import all data
    import all files
    import data 
    import files 
    Odoo import transfer import stock transfer import receipt import odoo import stock transfers import tranfers
    Odoo data import
    Odoo import
    odoo data importer
    odoo import tool
    odoo import module
    import Odoo app
    import tool 

	all import app
	all import app
	all in one import app
	all in one import module
	all inone import module
	This module use for following easy import.
	Import Stock from CSV and Excel file.
    Import Stock inventory from CSV and Excel File.
	Import inventory adjustment, import stock balance
	Import opening stock balance from CSV and Excel File.
	Import opening Bill of Material from CSV and Excel file.
	Import Sale order, Import sales order, Import Purchase order, Import purchases.
	Import sale order line, import purchase order line, Import data, Import files, Import data from third party software
	Import invoice from CSV, Import Bulk invoices easily.Import warehouse data,Import warehouse stock.Import product stock.
	Invoice import from CSV, Invoice line import from CSV, Sale import, purchase import
	Inventory import from CSV, stock import from CSV, Inventory adjustment import, Opening stock import. 
	Import product from CSV, Import customer from CSV, Product Import,Customer import, Odoo CSV bridge,Import CSV brige on Odoo.Import csv data on odoo.All import, easy import, Import odoo data, Import CSV files, Import excel files 
	Import tools Odoo, Import reports, import accounting data, import sales data, import purchase data, import data in odoo, import record, Import inventory.import data on odoo,Import data from Excel, Import data from CSV.Odoo Import Data

	-Import product from CSV and Excel file.
	-Import product from Excel and CSV file.
	-Import variant from CSV and Excel file.
	-Import variant from Excel and CSV file.
	-Import product variant from CSV and Excel file.
	-Import product variant from Excel and CSV file.

	-Import item from CSV and Excel file.
	-Import item from Excel and CSV file.

	-Import partner from CSV and Excel file.
	-Import vendor from csv and excel file
	-Import partner from Excel and CSV file.
	-Import contact from CSV and Excel file.
	-Import contact from Excel and CSV file.
	-Import Customer from CSV and Excel file.
	-Import customer from Excel and CSV file.
	-Import Supplier from CSV and Excel file.
	-Import supplier from Excel and CSV file.

	item import from csv, product import from csv, catelog import from csv, contact import from csv, partner import from csv, customer import from csv, supplier import from csv,variant import from csv, product variant import from csv, vendor import from csv
from
	item import from excel, product import from excel, catelog import from excel, contact import from excel, partner import from excel, customer import from excel, supplier import from excel,variant import from excel, product variant import from excel, vendor import from excel

	item import csv, product import csv, catelog import csv, contact import csv, partner import csv, customer import csv, supplier import csv,variant import csv, product variant import csv, vendor import csv

	item import excel, product import excel, catelog import excel, contact import excel, partner import excel, customer import excel, supplier import excel,variant import excel, product variant import excel, vendor import excel

	Import Bank Statement Lines from Excel
	Import Cash Statement Lines from Excel
	This module allow you to Import Bank Statement Lines from Excel file.
	This module will allow import Bank and Cash Statements from EXCEL.
	This module will allow import Cash Register Statements From Excel.
	Import Bank Statement Lines from CSV
	Import Cash Statement Lines from CSV
	This module allow you to Import Bank Statement Lines from CSV file.
	This module will allow import Bank and Cash Statements from CSV.
	This module will allow import Cash Register Statements from CSV.
	Excel Bank statement import
	CSV bank statement import
	Excel Cash statement import
	CSV cash statement import
	import bulk statement line, import statement lines
	This module use for import bulk bank statement lines from Excel file. Import statement lines from CSV or Excel file.
	Import statements, Import statement line, Bank statement Import, Add Bank statement from Excel.Add Excel Bank statement lines.Add CSV file.Import invoice data. Import excel file

	Import Bank Statement Lines from XLS
	Import Cash Statement Lines from XLS
	This module allow you to Import Bank Statement Lines from XLS file.
	This module will allow import Bank and Cash Statements from XLS.
	This module will allow import Cash Register Statements From XLS.
	xls Bank statement import
	xls Cash statement import

	-Import payment from CSV and Excel file.
    -Import customer payment from CSV and Excel file.
	-Import supplier payment, import accouting payment
	-Import customer and supplier payment from CSV and Excel file.
	-Import customer/supplier payment, Import payment details, Import payment entry, Import payment order.
	-Import payment invoice, import vendor payment, Import data, Import files, Import data from third party software
	-Import customer payment from CSV, Import Bulk payment easily.Import payment data,Import payment order.Import accounting entry.
	-customer payment import from CSV, supplier payment import from CSV, customer payment order import, supplier payment order import
    -Customer import, Odoo CSV bridge,Import CSV brige on Odoo

        Its also usefull for import opening stock balance with serial number from XLS or CSV file.
	-Import Stock from CSV and Excel file.
        -Import Stock inventory from CSV and Excel file.
	-Import inventory adjustment, import stock balance
	-Import opening stock balance from CSV and Excel file.
	-Inventory import from CSV, stock import from CSV, Inventory adjustment import, Opening stock import. Import warehouse stock, Import product stock.Manage Inventory, import inventory with lot number, import inventory with serial number, import inventory adjustment with serial number, import inventory adjustment with lot number. import inventory data, import stock data, import opening stock with lot number, import lot number, import serial number. 

	This module use for import bulk invoice lines from Excel file. Import invoice lines from CSV or Excel file.
	Import invoices, Import invoice line, Import invoice lines, Invoice Import, Add invoice from Excel.Add Excel invoice lines.Add CSV file.Import invoice data. Import excel file

	Used to import the journal entry from the xls or csv data file. 
	import accounting transection from CSV, Import journal entry from CSV, import journal item from CSV, import accouting data from CSV, import account move from CSV, import account move line from CSV.
	import accounting transection from Excel, Import journal entry from Excel, import journal item from Excel, import accouting data from Excel, import account move from Excel, import account move line from Excel.
	import accounting transection from XLS, Import journal entry from XLS, import journal item from XLS, import accouting data from XLS, import account move from XLS, import account move line from XLS.

	Import Sales, Import Sale order line, Import Sale lines, Import SO Line. Sale Import, Add SO from Excel.Add Excel Sale order lines.Add CSV file.Import Sale data. Import excel file

	This module use for import bulk purchase Order lines from Excel file. Import purchase order lines from CSV or Excel file.
	Import purchases, Import purchase order line, Import purchase lines, Import PO Line. purchase Import, Add PO from Excel.Add Excel Purchase order lines.Add CSV file.Import Purchase data. Import excel file

		Import product data with supplier details, import supplier info on product, import product suplier info, import product with vendor details, import supplier data on product.

	This apps helps to import incoming shipment and delivery order from Excel or CSV file.
	This module use for import bulk picking from Excel file. Import Delivery order from CSV or Excel file.
	Import Shipment, Import incoming shipment, Import Delivery Orders, Import Internal Transfer.Add Excel from Picking .Add CSV file.Import picking data. Import excel file

Stock de importación de CSV y archivo de Excel.
    Inventario de importación de archivo CSV y Excel.
Importar ajuste de inventario, importar saldo de stock
Importar saldo de stock de apertura de archivo CSV y Excel.
Importar pedido de venta, Importar pedido de cliente, Importar pedido de compra, Importar compras.
Importar línea de orden de venta, importar línea de orden de compra, Importar datos, Importar archivos, Importar datos de software de terceros
Importación de factura de CSV, Importación de facturas masivas fácilmente. Importe datos de almacén, importe stock de stock. Importe de stock de producto.
Importación de factura desde CSV, importación de línea de factura desde CSV, importación de venta, importación de compra
Importación de inventario desde CSV, importación de stock desde CSV, importación de ajuste de inventario, apertura de stock de importación.
Importar producto desde CSV, Importar cliente desde CSV, Importar producto, Importar cliente, Odoo CSV bridge, Importar CSV brige en Odoo. Importar datos csv en odoo. All import, easy import, Import odoo data, Import CSV files, Import excel files 
Importar herramientas Odoo, Importar informes, importar datos de contabilidad, importar datos de ventas, importar datos de compras, importar datos en odoo, importar registros, importar datos de inventory.import en odoo,Importar dados de Excel, Importar dados de CSV.Odoo Importar Dados

    odoo import product images import images on product multiple images on product Import bulk images on product.
    odoo Import product photos Import from product images from link Import images form link 
    odoo Import from other website Add product image Update product Image Import picture of product
    odoo import images from CSV Import product images from CSV file Import product data from CSV file
    odoo Set product image from URL Import product images from URL Import product images from CSV file IMport product image from URL
    odoo Multi product images Mutiple images on product website multi images import for product template

    odoo import images import images from url import multiple images on product Import bulk image on product variants
    odoo Import photos from url Import images from link Import product images form link 
    odoo Import image from other website Add image Update Image Import picture of prodduct template
    odoo import images from CSV Import product images from CSV file Import product data from CSV file
    odoo Set image from URL Import image from URL Import images from CSV file Import image from URL link
    odoo Multi images import Mutiple images form url website product multi images import for product variants

    odoo import product images import images on product multiple images on product Import bulk images on product
    odoo Import product photos Import from product images from link Import images form link
    odoo Import from other website Add product image Update product Image.Import picture of product
    odoo import images from CSV Import product images from CSV file Import product data from CSV file 
    odoo Set product image from URL Import product images from URL Import product images from CSV file
    odoo Import product image from URL Multi product images import Mutiple images on product website multi images
    odoo import product image odoo import image Import from product images from path Import images form local system path. 
    odoo Import images directory Add product image Update product Image Import picture of product 
    odoo import images from excel Import product images from excel file Import product data from excel file. 
    odoo Set product image from excel Import product images from excel Import product images from excel file. 
    odoo IMport product image from excel import image from csv import image from excel import image from zip file



    Import vendor and sale pricelist from Excel and CSV File.
    import sale pricelist
    import pricelist from excel
    import pricelist from csv
    import vendor pricelist from excel
    import vendor pricelist from csv
    import supplier pricelist from excel
    import supplier pricelist from csv
    import purchase pricleist from excel
    import purchase pricelist from csv
    import sales pricelist from excel
    import customer pricelist from excel
    import sales pricelist from csv
    import customer pricelist from csv
    import pricelist on Odoo

-Importar producto desde CSV y archivo de Excel.
-Importar producto de archivo Excel y CSV.
- Variante de importación de archivo CSV y Excel.
- Variante de importación de archivos Excel y CSV.
- Variante de producto de importación de CSV y archivo de Excel.
- Variante de producto de importación de archivos Excel y CSV.

-Importa elemento de archivo CSV y Excel.
-Importa elemento de archivo Excel y CSV.

- Socio de importación de CSV y archivo de Excel.
- Proveedor de importación desde csv y archivo excel
- Socio de importación desde el archivo Excel y CSV.
-Importar contacto desde CSV y archivo de Excel.
-Importar contacto desde el archivo Excel y CSV.
-Importar al cliente desde el archivo CSV y Excel.
-Importar cliente desde el archivo Excel y CSV.
- Proveedor de importación de archivos CSV y Excel.
- Proveedor de importación de archivos Excel y CSV.

importación de csv, importación de csv, importación de catelog de csv, importación de csv, importación de csv, importación de csv, importación de csv, importación de csv, variante de importación de csv, importación de csv

importación de Excel, importación de Excel, importación de Excel, importación de Excel, importación de Excel, importación de Excel, importación de Excel, importación de Excel, importación de Excel

artículo importación csv, producto importación csv, catelog importación csv, contacto importación csv, socio importación csv, cliente importación csv, proveedor importación csv, variante importación csv, producto variante importación csv, vendedor importación csv

item import excel, producto import excel, catelog import excel, contacto import excel, socio import excel, cliente import excel, proveedor import excel, variante import excel, variante del producto import excel, vendedor import excel

Importer le stock à partir du fichier CSV et Excel.
    Importer l'inventaire des stocks à partir du fichier CSV et Excel.
Importer l'ajustement de l'inventaire, importer le solde du stock
Importer le solde du stock d'ouverture à partir du fichier CSV et Excel.
Pedido de Venda de Importação, Pedido de Compra de Importação, Pedido de Compra de Importação, Compras de Importação.
Linha de pedidos de venda de importação, linha de pedido de importação de importação, Importar dados, Importar arquivos, Importar dados de software de terceiros
Importe a fatura da CSV, imprima as faturas em massa facilmente. Importe os dados do armazém, Importe o estoque do armazém. Importe o estoque do produto.
Importação de fatura de CSV, importação de linha de fatura de CSV, importação de venda, importação de compra
Importação de inventário de CSV, importação de estoque de CSV, importação de ajuste de estoque, importação de estoque de abertura.
Importar produto da CSV, Importar cliente da CSV, Importação de produtos, importação de clientes, ponte Odoo CSV, Importar CSV brige em Odoo.Importar dados csv em odoo. Toda importação, importação fácil, Importar dados odoo, Importar arquivos CSV, Importar arquivos excel
Importar ferramentas Odoo, Importar relatórios, importar dados contábeis, importar dados de vendas, importar dados de compras, importar dados em odoo, importar registro, Importar dados de inventário.importado em odoo, Importar dados do Excel, Importar dados de CSV.Odoo Importar Dados

-Importe o produto do arquivo CSV e Excel.
-Importe o produto do arquivo Excel e CSV.
Variante de importação do arquivo CSV e Excel.
Variante de importação do arquivo Excel e CSV.
-Importar variante de produto do arquivo CSV e Excel.
-Importar variante de produto do arquivo Excel e CSV.

-Importe item do arquivo CSV e Excel.
-Importe item do arquivo Excel e CSV.

-Importar parceiro do arquivo CSV e Excel.
Fornecedor de importação do arquivo csv e excel
-Importar parceiro do arquivo Excel e CSV.
-Importe contato de arquivos CSV e Excel.
-Introduza o contato do arquivo Excel e CSV.
-Importe o cliente do arquivo CSV e Excel.
-Importar cliente do arquivo Excel e CSV.
Fornecedor de importação de arquivos CSV e Excel.
Fornecedor de importação do arquivo Excel e CSV.

importação de item de csv, importação de produto de csv, importação de catell de csv, importação de contatos de csv, importação de parceiros de csv, importação de clientes de csv, importação de fornecedor de csv, importação de variante de csv, importação de variante de produto de csv, importação de vendedor de csv

importação de itens de excel, importação de produtos de excel, importação de catelog de excel, importação de contato de excel, importação de parceiros de excel, importação de clientes de excel, importação de fornecedor de excel, importação variante de excel, importação de variante de produto de excel, importação de fornecedor de excel

csv de importação de produto, csv de importação de produto, csv de importação de produto, csv de importação de csv, importação de csv de contato, csv de importação de cliente, csv de importação de cliente, csv de importação de importação, csv de importação de variante, csv de importação de variante, csv de importação de fornecedor

item import excel, importação de produto excel, catelog import excel, contato import excel, import do parceiro excel, import do cliente excel, fornecedor import excel, variante import excel, variante do produto import excel, import do vendedor excel

Importar linhas de declaração bancária da XLS
Importar Linhas de Declaração de Caixa de XLS
Este módulo permite que você importe Linhas de declaração bancária do arquivo XLS.
Este módulo permitirá a importação de notas de banco e dinheiro da XLS.
Este módulo permitirá a importação de declarações de caixa registradora da XLS.
xls Importação do banco
xls Importe de demonstração de caixa

- Importe o pagamento de arquivos CSV e Excel.
    - Importe o pagamento do cliente a partir do arquivo CSV e Excel.
-Preferimento do fornecedor de importação, importação de pagamento em conta
-Importe o pagamento do cliente e do fornecedor a partir do arquivo CSV e Excel.
-Importe o pagamento do cliente / fornecedor, detalhes do pagamento de importação, entrada de pagamento de importação, ordem de pagamento de importação.
-Importar a factura de pagamento, importar o pagamento do vendedor, importar dados, importar arquivos, importar dados de software de terceiros
- Importe o pagamento do cliente da CSV, Importe o pagamento a granel fácilmente. Importe os dados de pagamento, Importe o pedido de pagamento. Importe a entrada contábil.
- importação de pagamento por cliente de CSV, importação de pagamento de fornecedor de CSV, importação de pedido de pagamento de cliente, importação de pedido de pagamento de fornecedor
    - Importación de clientes, puente CSV Odoo, importación CSV brige sobre Odoo

        Il est également utile pour importer le solde d'ouverture avec le numéro de série du fichier XLS ou CSV.
-Import Stock à partir de fichiers CSV et Excel.
        -Import Stock d'inventaire à partir de fichiers CSV et Excel.
- Ajustement de l'inventaire des importations, importation du solde du stock
- Importer le solde du stock d'ouverture du fichier CSV et Excel.
-Inventaire d'importation de CSV, importation d'actions de CSV, importation d'ajustement d'inventaire, importation d'actions d'ouverture. Stock d'entrepôt d'importation, stock d'importation de produit. Gérer l'inventaire, importer l'inventaire avec le numéro de lot, importer l'inventaire avec le numéro de série, importer l'ajustement d'inventaire avec le numéro de série, importer l'ajustement d'inventaire avec le numéro de lot. importer des données d'inventaire, importer des données de stock, importer des actions d'ouverture avec le numéro de lot, importer le numéro de lot, importer le numéro de série.

Importar líneas de extracto bancario de XLS
Importar líneas de extracto de caja de XLS
Este módulo le permite importar líneas de extractos bancarios del archivo XLS.
Este módulo permitirá la importación de estados de cuenta bancarios y de efectivo de XLS.
Este módulo permitirá la importación de extractos de caja de XLS.
xls importación de extracto bancario
xls Importación de extracto de caja

-Importar el pago desde el archivo CSV y Excel.
    -Importar el pago del cliente desde el archivo CSV y Excel.
- Pago del proveedor de la importación, importación que acusa el pago
-Importar el pago de clientes y proveedores desde el archivo CSV y Excel.
-Importar pago de cliente / proveedor, Importar detalles de pago, Importar entrada de pago, Importar orden de pago.
-Importar factura de pago, importar el pago del proveedor, Importar datos, Importar archivos, Importar datos de software de terceros
- Importe el pago del cliente de CSV, importe el pago a granel fácilmente. Importe datos del pago, importe la orden del pago. Entrada de la contabilidad de la importación.
- importación de pago de cliente de CSV, importación de pago de proveedor de CSV, importación de orden de pago de cliente, importación de orden de pago de proveedor
    - Importación de clientes, puente CSV Odoo, importación de brige CSV en Odoo

        También es útil para la importación de saldos iniciales con número de serie de archivo XLS o CSV.
-Importar Stock desde CSV y archivo de Excel.
        Inventario de acciones de archivo CSV y Excel.
- Ajuste de inventario de importación, balance de stock de importación
-Importar la apertura de stock de CSV y archivo de Excel.
- Inventario importado de CSV, importación de stock desde CSV, importación de ajuste de inventario, apertura de stock de importación. Importar stock de almacén, Importar inventario de producto. Administrar inventario, importar inventario con número de lote, importar inventario con número de serie, importar ajuste de inventario con número de serie, importar ajuste de inventario con número de lote. importar datos de inventario, importar datos de stock, importar stock de apertura con número de lote, número de lote de importación, número de serie de importación.

Este módulo se utiliza para importar líneas de factura a granel desde el archivo de Excel. Importar líneas de factura desde el archivo CSV o Excel.
Importación de facturas, Importar línea de factura, Importar líneas de factura, Importación de factura, Agregar factura de Excel.Añadir líneas de factura de Excel.Añadir archivo CSV.Importar datos de factura. Importar archivo de Excel

Se usa para importar la entrada del diario desde el archivo de datos xls o csv.
importar la transección de contabilidad desde CSV, Importar entrada de diario desde CSV, Importar artículo de diario desde CSV, Importar datos de asignación desde CSV, Importar movimiento de cuenta desde CSV, Importar mover línea desde CSV.
importar la sección de contabilidad desde Excel, importar la entrada de diario de Excel, importar el artículo de diario de Excel, importar los datos de asignación de Excel, importar mover la cuenta de Excel, importar la línea de movimiento de cuenta desde Excel.
importe la transección de contabilidad de XLS, importe la entrada de diario de XLS, importe el artículo de diario de XLS, importe los datos de asignación de XLS, importe la transferencia de cuenta desde XLS, importe la línea de movimiento de cuenta desde XLS.

Importar ventas, Línea de orden de venta de importación, Importar líneas de venta, Importar línea SO. Importación de venta, agregue SO de Excel. Agregue líneas de orden de venta de Excel. Agregue archivo CSV. Importe de datos de venta. Importar archivo de Excel

Este módulo se usa para la importación de compras a granel Ordene líneas desde el archivo de Excel. Importe líneas de orden de compra desde CSV o archivo de Excel.
Importar compras, Importar línea de orden de compra, Importar líneas de compra, Importar línea de pedido. comprar Importar, Agregar PO desde Excel. Agregar líneas de orden de compra de Excel. Agregar archivo CSV. Importar datos de compra. Importar archivo de Excel

Importe los datos del producto con los detalles del proveedor, importe la información del proveedor del producto, importe la información del proveedor del producto, importe el producto con los datos del proveedor, importe los datos del proveedor del producto.

Esta aplicación ayuda a importar el envío entrante y la orden de entrega desde el archivo Excel o CSV.
Este módulo se usa para importar picking a granel desde el archivo de Excel. Importar orden de entrega desde CSV o archivo de Excel.
Importar envío, Importar envío entrante, Importar pedidos de entrega, Importar transferencia interna.Añadir Excel de Elegir. Añadir archivo CSV. Importar datos de picking. Importar archivo de Excel

Este módulo se usa para importar imágenes de productos, importar imágenes en el producto, múltiples imágenes en el producto, importar imágenes a granel en el producto. Importación de fotos del producto, Importar imágenes del producto desde el enlace, Importar imágenes en el enlace. Importar desde otro sitio web. Agregar imagen del producto. Actualice la imagen del producto. Importe la imagen del producto, importe las imágenes desde CSV, importe las imágenes del producto desde el archivo CSV, importe los datos del producto desde el archivo CSV. Establecer imagen de producto desde URL, importar imágenes de producto desde URL, importar imágenes de producto desde archivo CSV. IMportar imagen del producto desde URL, imágenes de productos múltiples, imágenes mutiples en producto, imágenes múltiples del sitio web

Importar desde imágenes de productos desde la ruta, importar imágenes desde la ruta del sistema local. Importar directorio de imágenes. Agregar imagen del producto. Actualice la imagen del producto. Importe la imagen del producto, importe las imágenes desde Excel, importe las imágenes del producto desde el archivo Excel, importe los datos del producto desde el archivo Excel. Establezca la imagen del producto desde Excel, Importe imágenes del producto desde Excel, Importe imágenes del producto desde el archivo Excel. IMportar imagen del producto de excel.

Este módulo usa para importar linhas de faturamento em massa a partir do arquivo do Excel. Importar linhas de faturamento de arquivos CSV ou Excel.
Importar facturas, Importar linha de fatura, Importar linhas de fatura, Importação de fatura, Adicionar fatura de Excel.Adicionar linhas de fatura do Excel. Adicionar arquivo CSV. Comunicar dados de fatura. Importar arquivo excel.

Usado para importar a entrada de diário do arquivo de dados xls ou csv.
importar transição contábil da CSV, importar diário de CSV, importar item do jornal de CSV, importar dados de cadastro de CSV, importar a conta, mover-se do CSV, importar a linha de movimento da conta de CSV.
importar transação contábil do Excel, importar entrada do diário do Excel, importar o item do jornal do Excel, importar dados de cadastro do Excel, importar a parte da conta do Excel, importar a linha de mover a conta do Excel.
importar transição contábil da XLS, importar entrada de jornal de XLS, importar item de jornal de XLS, importar dados de caducidade de XLS, importar conta mover de XLS, importar linha de movimento de conta de XLS.

Importar vendas, linha de pedidos de importação de importação, linhas de importação de vendas, importar SO Line. Importação de vendas, Adicione SO de Excel. Adicione as linhas de ordem de venda do Excel. Adicione arquivos CSV. Dados de venda de importação. Importar arquivo excel

Este módulo usa para importar compras a granel Ordem linhas do arquivo do Excel. Importar linhas de pedidos de compra a partir do arquivo CSV ou Excel.
Compra de importação, Importar linha de pedido de compra, Importar linhas de compra, Importar linha de pagamento. Compre Importação, Adicione PO a partir do Excel. Adicione as linhas de ordem de compra Excel. Adicione arquivo CSV. Dados de compra de importação. Importar arquivo excel

Importe os dados do produto com os detalhes do fornecedor, importe as informações do fornecedor sobre o produto, importe as informações do fornecedor, importe o produto com os detalhes do fornecedor, importe os dados do fornecedor no produto.

Essas aplicações ajudam a importar a ordem de envio e entrega do arquivo Excel ou CSV.
Este módulo é usado para seleção de volume de importação do arquivo do Excel. Importar a ordem de entrega do arquivo CSV ou Excel.
Envio de Importação, Importe o carregamento recebido, Importar Pedidos de Entrega, Importar Transferência Interna. Adicionar o Excel de Escolher .Adicionar o arquivo CSV. Importação de dados de seleção. Importar arquivo excel

Este módulo usa para importar imagens de produtos, importar imagens no produto, várias imagens no produto, Importar imagens em massa no produto. Importar fotos de produtos, Importar de imagens de produtos do link, Importar link de formulário de imagens. Importar de outro site. Adicione a imagem do produto. Atualize a imagem do produto. Importe a imagem do produto, importe imagens do CSV, Importe imagens do produto do arquivo CSV, Importe dados do produto do arquivo CSV. Defina a imagem do produto a partir de URL, Importe imagens de produtos de URL, Importe imagens de produtos do arquivo CSV. Importe a imagem do produto a partir de URL, imagens de vários produtos, imagens Mutiple no produto, imagens multi site

Importar imagens do produto do caminho, importar imagens do caminho do sistema local. Importar o diretório de imagens. Adicione a imagem do produto. Atualize a imagem do produto. Importe a imagem do produto, importe imagens do Excel, Importe imagens do produto do arquivo excel, Importe os dados do produto do arquivo excel. Defina a imagem do produto a partir do excel, Importe imagens do produto do Excel, Importe imagens do produto do arquivo excel. IMPORTE a imagem do produto do excel.

    """,
    'author': 'BROWSEINFO',
    'license': 'OPL-1',
    'website': 'https://www.browseinfo.com/demo-request?app=bi_generic_import&version=18&edition=Community',
    'live_test_url': 'https://www.browseinfo.com/demo-request?app=bi_generic_import&version=18&edition=Community',
    'depends': ['base', 'sale_management', 'account', 'purchase', 'stock', 'product_expiry',
                'bi_import_chart_of_accounts', 'hr_attendance', 'hr_timesheet', 'crm', 'hr_expense'],
    'data': [
        "security/import_security.xml",
        'security/ir.model.access.csv',
        'data/data.xml',
        "data/attachment_sample.xml",
        "wizard/sale.xml",
        "wizard/import_employee_timesheet_view.xml",
        "wizard/wizard_import_view.xml",
        "wizard/import_lot_with_stock.xml",
        "wizard/import_lead_view.xml",
        "wizard/import_res_users.xml",
        "wizard/export_products_images_wizard_view.xml",
        "views/import_invoice_log_views.xml",
        "views/account_invoice.xml",
        "views/purchase_invoice.xml",
        "views/sale.xml",
        "views/task.xml",
        "views/stock_view.xml",
        "views/product_view.xml",
        "views/partner.xml",
        "views/picking_view.xml",
        "views/customer_payment.xml",
        "views/import_order_lines_view.xml",
        "views/import_po_lines_view.xml",
        "views/import_invoice_lines_view.xml",
        "views/bank_statement.xml",
        "views/account_move.xml",
        "views/supp_info.xml",
        "views/pricelist.xml",
        "views/product_variant.xml",
        "views/import_dashboard_action.xml",
        "views/import_image_view.xml",
        "views/attendances.xml",
        "views/invoice_with_payment.xml",
        "views/multiple_account_move.xml",
        "views/stock_gen_view.xml",
        "views/expense_import_wizard_view.xml",
        "views/import_lot_serial_no_view.xml",
        "views/import_dashboard.xml",
    ],
    'qweb': [
    ],
    'demo': [],
    'test': [],
    'installable': True,
    'application': True,
    'auto_install': False,
    'images': ['static/description/Banner.gif'],

}
