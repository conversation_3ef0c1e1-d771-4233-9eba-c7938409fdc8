/* Sale Product Image Styles */

/* Fixed size for product images in sale order lines */
.o_image_40_cover {
    height: 40px !important;
    width: 40px !important;
    max-height: 40px !important;
    max-width: 40px !important;
}

/* Style for the image field widget */
.o_image_40_cover .o_field_image {
    height: 40px !important;
    width: 40px !important;
    max-height: 40px !important;
    max-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Style for the actual img tag */
.o_image_40_cover .o_field_image img {
    height: 40px !important;
    width: 40px !important;
    max-height: 40px !important;
    max-width: 40px !important;
    object-fit: cover !important;
    border-radius: 4px !important;
    border: 1px solid #dee2e6 !important;
}

/* Style for empty image placeholder */
.o_image_40_cover .o_field_image .fa-file-image-o,
.o_image_40_cover .o_field_image .fa-camera {
    font-size: 18px !important;
    color: #6c757d !important;
    width: 40px !important;
    height: 40px !important;
    line-height: 40px !important;
    text-align: center !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    background-color: #f8f9fa !important;
}

/* Ensure table cell has fixed width */
.o_list_table td.o_image_40_cover,
.o_list_table th.o_image_40_cover {
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
    padding: 5px !important;
    text-align: center !important;
}
