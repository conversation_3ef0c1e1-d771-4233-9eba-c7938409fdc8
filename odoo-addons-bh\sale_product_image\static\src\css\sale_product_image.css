/* Sale Product Image Styles */

/* Fixed size for product images in sale order lines */
.o_sale_order_line_with_image .o_image_40_cover {
    height: 40px !important;
    width: 40px !important;
    max-height: 40px !important;
    max-width: 40px !important;
    object-fit: cover !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
}

/* Ensure the image widget container also has fixed size */
.o_sale_order_line_with_image .o_image_40_cover .o_field_image {
    height: 40px !important;
    width: 40px !important;
    max-height: 40px !important;
    max-width: 40px !important;
}

/* Style for the actual img tag inside the widget */
.o_sale_order_line_with_image .o_image_40_cover .o_field_image img {
    height: 40px !important;
    width: 40px !important;
    max-height: 40px !important;
    max-width: 40px !important;
    object-fit: cover !important;
    border-radius: 4px !important;
}

/* Style for empty image placeholder */
.o_sale_order_line_with_image .o_image_40_cover .o_field_image .fa-file-image-o {
    font-size: 20px !important;
    line-height: 40px !important;
    width: 40px !important;
    height: 40px !important;
    text-align: center !important;
}

/* Ensure column width is fixed */
.o_sale_order_line_with_image .o_list_table th:has(.o_image_40_cover),
.o_sale_order_line_with_image .o_list_table td:has(.o_image_40_cover) {
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
    padding: 5px !important;
}
