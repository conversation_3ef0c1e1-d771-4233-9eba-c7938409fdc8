/* Sale Product Image Styles */

/* Fixed size for product images in sale order lines */
.o_image_70_cover {
    height: 70px !important;
    width: 70px !important;
    max-height: 70px !important;
    max-width: 70px !important;
}

/* Style for the image field widget */
.o_image_70_cover .o_field_image {
    height: 70px !important;
    width: 70px !important;
    max-height: 70px !important;
    max-width: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Style for the actual img tag */
.o_image_70_cover .o_field_image img {
    height: 70px !important;
    width: 70px !important;
    max-height: 70px !important;
    max-width: 70px !important;
    object-fit: cover !important;
    border-radius: 4px !important;
    border: 1px solid #dee2e6 !important;
}

/* Style for empty image placeholder */
.o_image_70_cover .o_field_image .fa-file-image-o,
.o_image_70_cover .o_field_image .fa-camera {
    font-size: 30px !important;
    color: #6c757d !important;
    width: 70px !important;
    height: 70px !important;
    line-height: 70px !important;
    text-align: center !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    background-color: #f8f9fa !important;
}

/* Ensure table cell has fixed width */
.o_list_table td.o_image_70_cover,
.o_list_table th.o_image_70_cover {
    width: 80px !important;
    min-width: 80px !important;
    max-width: 80px !important;
    padding: 5px !important;
    text-align: center !important;
}
