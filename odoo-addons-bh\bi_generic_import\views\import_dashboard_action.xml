<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Open sale order view -->
    <record id="action_sale_order_import_view" model="ir.actions.act_window">
        <field name="name">Sale Order</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="sale.view_order_tree"/>
        <field name="search_view_id" ref="sale.sale_order_view_search_inherit_sale"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a sale order
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open purchase order view -->
    <record id="action_purchase_order_import_view" model="ir.actions.act_window">
        <field name="name">Purchase Order</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="purchase.purchase_order_tree"/>
        <field name="search_view_id" ref="purchase.view_purchase_order_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a purchase order
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open Invoice/bills view -->
    <record id="action_invoice_bills_import_view" model="ir.actions.act_window">
        <field name="name">Invoice / Bills</field>
        <field name="res_model">account.move</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True),('move_type', 'in',
            ['in_invoice','out_invoice','out_refund','in_refund'])]
        </field>
        <field name="view_id" ref="account.view_invoice_tree"/>
        <field name="search_view_id" ref="account.view_account_invoice_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Invoice / Bills
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open shippment/delivery view -->
    <record id="action_picking_import_view" model="ir.actions.act_window">
        <field name="name">Delivery / shipment</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="stock.vpicktree"/>
        <field name="search_view_id" ref="stock.view_picking_internal_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Delivery / Shipment
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open mrp view -->
    <!--record id="action_mrp_import_view" model="ir.actions.act_window">
        <field name="name">Bill Of material</field>
        <field name="res_model">mrp.bom</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="mrp.mrp_bom_tree_view"/>
        <field name="search_view_id" ref="mrp.view_mrp_bom_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Bill Of Material
            </p>
            <p>

            </p>
        </field>
    </record-->

    <!-- Open partner view -->
    <record id="action_partner_import_view" model="ir.actions.act_window">
        <field name="name">Partner / vendor</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="base.res_partner_kanban_view"/>
        <field name="search_view_id" ref="base.view_res_partner_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a partner
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open pricelist view -->
    <record id="action_pricelist_import_view" model="ir.actions.act_window">
        <field name="name">pricelist</field>
        <field name="res_model">product.pricelist</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="product.product_pricelist_view_tree"/>
        <field name="search_view_id" ref="product.product_pricelist_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a pricelist
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open template view -->
    <record id="action_product_template_import_view" model="ir.actions.act_window">
        <field name="name">Product Template</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="product.product_template_kanban_view"/>
        <field name="search_view_id" ref="product.product_template_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a product
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open variant view -->
    <record id="action_product_product_import_view" model="ir.actions.act_window">
        <field name="name">Product Varaint</field>
        <field name="res_model">product.product</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_var', '=', True)]</field>
        <field name="view_id" ref="product.product_product_tree_view"/>
        <field name="search_view_id" ref="product.product_search_form_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a product Variant
            </p>
            <p>

            </p>
        </field>
    </record>

    <!-- Open stock quant view -->
    <record id="action_stock_quant_import_view" model="ir.actions.act_window">
        <field name="name">Inventory</field>
        <field name="res_model">stock.quant</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="stock.view_stock_quant_tree_inventory_editable"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Inventory Adjustment
            </p>
            <p>
            </p>
        </field>
    </record>

    <record id="action_account_payment_import_view" model="ir.actions.act_window">
        <field name="name">Payment</field>
        <field name="res_model">account.payment</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="account.view_account_payment_tree"/>
        <field name="search_view_id" ref="account.view_account_payment_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a payment
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_project_task_import_view" model="ir.actions.act_window">
        <field name="name">Task</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="project.view_task_tree2"/>
        <field name="search_view_id" ref="project.view_task_search_form_project_fsm_base"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a task
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_product_image_import_view" model="ir.actions.act_window">
        <field name="name">Product Image</field>
        <field name="res_model">product.product</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_img_import', '=', True)]</field>
        <field name="view_id" ref="product.product_kanban_view"/>
        <field name="search_view_id" ref="product.product_search_form_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a product image
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_product_image_zip_import_view" model="ir.actions.act_window">
        <field name="name">Product Image Zip</field>
        <field name="res_model">product.product</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_img_zip_import', '=', True)]</field>
        <field name="view_id" ref="product.product_kanban_view"/>
        <field name="search_view_id" ref="product.product_search_form_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a product image zip
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_attendace_import_view" model="ir.actions.act_window">
        <field name="name">Attendance</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="hr_attendance.view_attendance_tree"/>
        <field name="search_view_id" ref="hr_attendance.hr_attendance_view_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a attendance
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_timesheet_import_view" model="ir.actions.act_window">
        <field name="name">Timesheet</field>
        <field name="res_model">account.analytic.line</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="hr_timesheet.hr_timesheet_line_tree"/>
        <field name="search_view_id" ref="hr_timesheet.hr_timesheet_line_my_timesheet_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a timesheet
            </p>
            <p>

            </p>
        </field>
    </record>

    <!--record id="action_pos_order_import_view" model="ir.actions.act_window">
        <field name="name">POS Order</field>
        <field name="res_model">pos.order</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="point_of_sale.view_pos_order_tree"/>
        <field name="search_view_id" ref="point_of_sale.view_pos_order_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a pos Order
            </p>
            <p>

            </p>
        </field>
    </record-->

    <record id="action_product_supplierinfo_import_view" model="ir.actions.act_window">
        <field name="name">Supplier Info</field>
        <field name="res_model">product.supplierinfo</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="product.product_supplierinfo_tree_view"/>
        <field name="search_view_id" ref="product.product_supplierinfo_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a product supplier
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_reorder_rule_import_view" model="ir.actions.act_window">
        <field name="name">Reordering Rules</field>
        <field name="res_model">stock.warehouse.orderpoint</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="stock.view_warehouse_orderpoint_tree_editable"/>
        <field name="search_view_id" ref="stock.warehouse_orderpoint_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a reordering rules
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_stock_lot_import_view" model="ir.actions.act_window">
        <field name="name">Lots/Serial Numbers</field>
        <field name="res_model">stock.lot</field>
        <field name="view_mode">list,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="stock.view_production_lot_tree"/>
        <field name="search_view_id" ref="stock.search_product_lot_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a lot/serial number
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_account_journal_import_view" model="ir.actions.act_window">
        <field name="name">Journal Entries</field>
        <field name="res_model">account.move</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_journal_import', '=', True),('move_type', '=', 'entry')]</field>
        <field name="view_id" ref="account.view_move_tree"/>
        <field name="search_view_id" ref="account.view_account_move_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Journal Entries
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_account_account_import_view" model="ir.actions.act_window">
        <field name="name">Chart of Accounts</field>
        <field name="res_model">account.account</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="account.view_account_list"/>
        <field name="search_view_id" ref="account.view_account_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a chart of accounts
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_crm_lead_import_view" model="ir.actions.act_window">
        <field name="name">Leads</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="crm.crm_case_tree_view_leads"/>
        <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a crm leads
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_res_users_import_view" model="ir.actions.act_window">
        <field name="name">Users</field>
        <field name="res_model">res.users</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="base.view_users_tree"/>
        <field name="search_view_id" ref="base.view_users_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a users
            </p>
            <p>
            </p>
        </field>
    </record>

    <record id="action_stock_quant_lot_import_view" model="ir.actions.act_window">
        <field name="name">Inventory</field>
        <field name="res_model">stock.quant</field>
        <field name="view_mode">list</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import_lot', '=', True)]</field>
        <field name="view_id" ref="stock.view_stock_quant_tree_inventory_editable"/>
        <field name="search_view_id" ref="stock.quant_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a inventory
            </p>
            <p>
            </p>
        </field>
    </record>

    <record id="action_hr_expense_import_view" model="ir.actions.act_window">
        <field name="name">Expenses</field>
        <field name="res_model">hr.expense</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import', '=', True)]</field>
        <field name="view_id" ref="hr_expense.view_my_expenses_tree"/>
        <field name="search_view_id" ref="hr_expense.hr_expense_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a expense
            </p>
            <p>
            </p>
        </field>
    </record>

    <record id="action_internal_transfer_import_view" model="ir.actions.act_window">
        <field name="name">Internal Transfer</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import_int_transfer', '=', True)]</field>
        <field name="view_id" ref="stock.vpicktree"/>
        <field name="search_view_id" ref="stock.view_picking_internal_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Internal transfer
            </p>
            <p>

            </p>
        </field>
    </record>

    <record id="action_product_template_export_view" model="ir.actions.act_window">
        <field name="name">Product Template</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{}</field>
        <field name="domain">[]</field>
        <field name="view_id" ref="product.product_template_kanban_view"/>
        <field name="search_view_id" ref="product.product_template_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a product
            </p>
            <p>
            </p>
        </field>
    </record>
    <record id="action_stock_lot_picking" model="ir.actions.act_window">
        <field name="name">Lots/Serial Picking</field>
        <field name="res_model">stock.lot</field>
        <field name="view_mode">list,form</field>
        <field name="context">{}</field>
        <field name="domain">[('is_import_picking', '=', True)]</field>
        <field name="view_id" ref="stock.view_production_lot_tree"/>
        <field name="search_view_id" ref="stock.search_product_lot_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a lot/serial number
            </p>
            <p>

            </p>
        </field>
    </record>

</odoo>