<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="import_dashboard_kanban_dashboard" model="ir.ui.view">
        <field name="name">import.dashboard.kanban</field>
        <field name="model">import.dashboard</field>
        <field name="arch" type="xml">
            <kanban class="oe_background_grey o_kanban_dashboard o_salesteam_kanban" create="0">
                <field name="name"/>
                <field name="state"/>
                <field name="import_data"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content o_kanban_get_form" style="width:100%;">
                            <div class="o_has_icon">
                                <div class="container o_kanban_card_content">
                                    <div class="row" style="flex-wrap: nowrap; width: 52%;">
                                        <div class="col-xs-6 o_kanban_primary_left">
                                            <div t-if="record.state.raw_value =='sale.order'" style="color:teal;margin-left: 10px;" name ="sale order">
                                                <left>
                                                    <i title="Sale Order" class="fa fa-shopping-cart fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='purchase.order'" style="color:teal;margin-left: 30px;" name ="purchase order">
                                                <left>
                                                    <i title="Purchase Order" class="fa fa-credit-card-alt fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='account.move'" style="color:teal;margin-left: 30px;" name ="account move">
                                                <left>
                                                    <i title="Invoice" class="fa fa-money fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='stock.picking'" style="color:teal;margin-left: 30px;" name ="stock picking">
                                                <left><i title="Picking" class="fa fa-shopping-bag fa-5x"/>
                                                </left>
                                            </div>
                                            <!--div t-if="record.state.raw_value =='mrp.bom'" style="color:teal;margin-left: 30px;" name ="mrp bom">
                                                <left><i title="BOM" class="fa fa-industry fa-5x"/>
                                                </left>
                                            </div-->
                                            <div t-if="record.state.raw_value =='res.partner'" style="color:teal;margin-left: 30px;" name ="res partner">
                                                <left><i title="Partner" class="fa fa-user fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='product.pricelist'" style="color:teal;margin-left: 30px;" name ="product pricelist">
                                                <left><i title="Pricelist" class="fa fa-list fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='product.template'" style="color:teal;margin-left: 30px;" name ="product template">
                                                <left><i title="Product" class="fa fa-product-hunt fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='product.product'" style="color:teal;margin-left: 30px;" name ="variant">
                                                <left><i title="Product Varaint" class="fa fa-product-hunt fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='stock.quant'" style="color:teal;margin-left: 30px;" name ="Inventory">
                                                <left><i title="Stock Quant"  class="fa fa-exchange fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='account.payment'" style="color:teal;margin-left: 30px;" name ="account payment">
                                                <left><i title="Payment" class="fa fa-credit-card fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='project.task'" style="color:teal;margin-left: 30px;" name ="project task">
                                                <left><i title="Task" class="fa fa-tasks fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='product.image'" style="color:teal;margin-left: 30px;" name ="product variant">
                                                <left><i title="Product Variant" class="fa fa-product-hunt fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='product.zip'" style="color:teal;margin-left: 30px;" name ="product zip">
                                                <left><i title="Product Image Zip" class="fa fa-product-hunt fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='hr.attendance'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i  title="Attendance" class="fa fa-credit-card-alt fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='import_timesheet.csv'" style="color:teal;margin-left: 30px;" name ="import timesheet">
                                                <left><i title="Timesheet" class="fa fa-clock-o fa-5x"/>
                                                </left>
                                            </div>
                                            <!--div t-if="record.state.raw_value =='pos.order'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="POS Order" class="fa fa-shopping-cart fa-5x"/>
							                    </left>
							                </div-->
							                <div t-if="record.state.raw_value =='supplier.info'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Supplier Info" class="fa fa-industry fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='reorder.rule'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Reorder Rule" class="fa fa-industry fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='stock.lot'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Lot/Serial Number" class="fa fa-list-ol fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='account.move.journal'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Journal Entry" class="fa fa-book fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='account.account'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Chart of Accounts" class="fa fa-bank fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='crm.lead'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Leads" class="fa fa-handshake-o fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='res.users'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="User" class="fa fa-users fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='invt.lot.serial'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Inventory with Lot/Serial" class="fa fa-list-ol fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='hr.expense'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Employee Expenses" class="fa fa-user-plus fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='internal.transfer'" style="color:teal;margin-left: 30px;">
							                    <left>
							                        <i title="Internal Transfer" class="fa fa-shopping-bag fa-5x"/>
							                    </left>
							                </div>
							                <div t-if="record.state.raw_value =='export.product.template'" style="color:teal;margin-left: 30px;" name ="export product template">
                                                <left><i title="Export Product" class="fa fa-product-hunt fa-5x"/>
                                                </left>
                                            </div>
                                            <div t-if="record.state.raw_value =='lot.picking'" style="color:teal;margin-left: 30px;" name ="export product template">
                                                <left><i title="Import Lot/Serial Picking" class="fa fa-list-ol fa-5x"/>
                                                </left>
                                            </div>
                                        </div>
                                        <div class="col-xs-6 o_kanban_primary_right" style="margin-left:4%;" >
                                            <div t-if="record.state.raw_value =='sale.order'">
                                                <a style="font-size: 20px;" name="%(action_sale_order_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Sale</span>

                                                </a>
                                                <br/>
                                                <br/>
                                                <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(gen_sale_import_wizard)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Sales</span>
                                                </button>                                           
                                            </div>

                                            <div t-if="record.state.raw_value == 'purchase.order'">
                                                <a  style="font-size: 20px;" name="%(action_purchase_order_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Purchase</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button  class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(gen_pur_wizard)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Purchase</span>
                                                </button>                                                
                                            </div>

                                            <div t-if="record.state.raw_value =='account.move'">
                                                <a  style="font-size: 20px;" name="%(action_invoice_bills_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Invoice / Bills / Credit Note</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button class="btn btn-sm btn-primary"  style="margin-top: 20%;" name="%(bi_gen_inv_wizard_action)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Invoice/Bills</span>
                                                </button>
                                            </div>

                                            <div t-if="record.state.raw_value =='stock.picking'">
                                                <a  style="font-size: 20px;" name="%(action_picking_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Shipment / Delivery</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button  class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(bi_gen_picking_wizard_action)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Picking</span>
                                                </button>
                                            </div>

                                            <!--div t-if="record.state.raw_value =='mrp.bom'">
                                                <a  style="font-size: 20px;" name="%(action_mrp_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Bill of Materials</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button class="btn btn-sm btn-primary"  style="margin-top: 20%;" name="%(bi_gen_bom_wizard_action)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import BOM</span>
                                                </button>
                                            </div--> 

                                            <div t-if="record.state.raw_value =='res.partner'">
                                                <a  style="font-size: 20px;" name="%(action_partner_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Partner</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button class="btn btn-sm btn-primary"  style="margin-top: 20%;" name="%(action_gen_partner_import_wizard)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Partner</span>
                                                </button>
                                            </div>

                                            <div t-if="record.state.raw_value =='product.pricelist'">
                                                <a  style="font-size: 20px;" name="%(action_pricelist_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Pricelist</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button class="btn btn-sm btn-primary"  style="margin-top: 20%;" name="%(action_import_sale_pricelist_wizard)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Pricelist</span>
                                                </button>
                                            </div>
                                            <div t-if="record.state.raw_value =='product.template'">
                                                <a  style="font-size: 20px;" name="%(action_product_template_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Product Template</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button class="btn btn-sm btn-primary"  style="margin-top: 20%;" name="%(gen_product_variant_import_wizard)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Product</span>
                                                </button>
                                            </div>                                      
                                            <div t-if="record.state.raw_value =='product.product'">
                                                <a  style="font-size: 20px;" name="%(action_product_product_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Product Variant</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button  class="btn btn-sm btn-primary" style="margin-top: 20%; width=100%;" name="%(gen_product_import_wizard)d" type="action">
                                                    <span class="o_label">Import Product Variant</span>
                                                </button>

                                            </div>
                                            <div t-if="record.state.raw_value =='stock.quant'">
                                                <a  style="font-size: 20px;" name="%(action_stock_quant_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Inventory</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button  class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(gen_inv_wizard)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Inventory</span>
                                                </button>
                                            </div>
                                            <div t-if="record.state.raw_value =='account.payment'" name ="account payment details">
                                                <a  style="font-size: 20px;" name="%(action_account_payment_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Payment</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button  class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(action_import_customer_payment)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Payment</span>
                                                </button>
                                            </div>
                                            
                                            <div t-if="record.state.raw_value =='project.task'">
                                                <a  style="font-size: 20px;" name="%(action_project_task_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Task</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button  class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(import_task_wizard_action)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Task</span>
                                                </button>
                                            </div> 
                                            <div t-if="record.state.raw_value =='product.image'">
                                                <a  style="font-size: 20px;" name="%(action_product_image_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Product Image</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button  class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(action_import_product_image)d" type="action">
                                                    <span class="o_label" style="white-space: nowrap;">Import Product Image</span>
                                                </button>
                                            </div>
                                            <div t-if="record.state.raw_value =='product.zip'">
                                                <a  style="font-size: 20px;" name="%(action_product_image_zip_import_view)d" type="action">
                                                    <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
                                                    <span class="o_label" style="padding-left:4px">Product Image From Zip</span>
                                                </a>
                                                <br/>
                                                <br/>
                                                <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(gen_sale_import_wizard_img)d" type="action">
							                        <span class="o_label">Import Product Image From Zip</span>
							                    </button>
                                            </div>
                                            <div t-if="record.state.raw_value =='hr.attendance'">
							                    <a style="font-size: 20px;" name="%(action_attendace_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Attendance</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(hr_attendance_import_action)d" type="action">
							                        <span class="o_label">Import Attendance</span>
							                    </button>                                           
							                </div>
							                <div t-if="record.state.raw_value =='import_timesheet.csv'">
							                    <a style="font-size: 20px;" name="%(action_timesheet_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Timesheet</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(import_employee_timesheet_wizard_action)d" type="action">
							                        <span class="o_label">Import Timesheet</span>
							                    </button>
							                </div>
							                <!--div t-if="record.state.raw_value =='pos.order'">
							                    <a style="font-size: 20px;" name="%(action_pos_order_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">POS Order</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(action_pos_import)d" type="action">
							                        <span class="o_label">Import Pos Order</span>
							                    </button>                                           
							                </div-->
							                <div t-if="record.state.raw_value =='supplier.info'">
							                    <a style="font-size: 20px;" name="%(action_product_supplierinfo_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Supplier Information</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(action_import_supplier_info_wizard)d" type="action">
							                        <span class="o_label">Import Supplier Information</span>
							                    </button>                                           
							                </div>
							                <div t-if="record.state.raw_value =='reorder.rule'">
							                    <a style="font-size: 20px;" name="%(action_reorder_rule_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Reordering Rules</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(reorder_rule_import_action)d" type="action">
							                        <span class="o_label">Import Reordering Rule</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='stock.lot'">
							                    <a style="font-size: 20px;" name="%(action_stock_lot_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Lot/Serial Numbers</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(stk_imp_wizard)d" type="action">
							                        <span class="o_label">Import Lot/Serial Number</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='account.move.journal'">
							                    <a style="font-size: 20px;" name="%(action_account_journal_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Journal Entry</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(action_import_multiple_account_move_line)d" type="action">
							                        <span class="o_label">Import Journal Entry</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='account.account'">
							                    <a style="font-size: 20px;" name="%(action_account_account_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Chart of Accounts</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(bi_import_chart_of_accounts.bi_act_chart_of_account)d" type="action">
							                        <span class="o_label">Import Chart of Account</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='crm.lead'">
							                    <a style="font-size: 20px;" name="%(action_crm_lead_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Leads</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(lead_wizard_action)d" type="action">
							                        <span class="o_label">Import Leads</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='res.users'">
							                    <a style="font-size: 20px;" name="%(action_res_users_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Users</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(import_users_wizard_action)d" type="action">
							                        <span class="o_label">Import Users</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='invt.lot.serial'">
							                    <a style="font-size: 20px;" name="%(action_stock_quant_lot_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Inventory with Lot/Serial</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(stock_gen_inv_wizard)d" type="action">
							                        <span class="o_label">Import Inventory with Lot/Serial</span>
							                    </button>
							                </div>
                                            <div t-if="record.state.raw_value == 'lot.picking'">
							                    <a style="font-size: 20px;" name="%(action_stock_lot_picking)d" type="action">
													<span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Import Lot/Serial Picking</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(lot_wizard_action)d" type="action">
							                        <span class="o_label">Import Lot/Serial Picking</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='hr.expense'">
							                    <a style="font-size: 20px;" name="%(action_hr_expense_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Employee Expenses</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(action_import_expense)d" type="action">
							                        <span class="o_label">Import Employee Expenses</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='internal.transfer'">
							                    <a style="font-size: 20px;" name="%(action_internal_transfer_import_view)d" type="action">
							                        <span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Internal Transfer</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(bi_gen_picking_wizard_action_inh)d" type="action">
							                        <span class="o_label">Import Internal Transfer</span>
							                    </button>
							                </div>
							                <div t-if="record.state.raw_value =='export.product.template'">
							                    <a style="font-size: 20px;" name="%(action_product_template_export_view)d" type="action">
													<span class="o_value"><t t-esc="record.import_data.raw_value"/></span>
							                        <span class="o_label" style="padding-left:4px">Products with Images</span>
							                    </a>
							                    <br/>
							                    <button class="btn btn-sm btn-primary" style="margin-top: 20%;" name="%(product_template_action_ext)d" type="action">
							                        <span class="o_label">Export Products with Images</span>
							                    </button>
							                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>


    <record id="import_kanban_dashboard_action" model="ir.actions.act_window">
        <field name="name">Import's Dashboard</field>
        <field name="res_model">import.dashboard</field>
        <field name="type">ir.actions.act_window</field>
        
        <field name="view_mode">kanban,form</field>
        <field name="view_id" ref="import_dashboard_kanban_dashboard"/>
        <field name="usage">menu</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
            </p>
        </field>
    </record>

    <menuitem
        action="import_kanban_dashboard_action"
        id="import_kanban_dashboard_view"
        parent="menu_dashboard"
        sequence="0"
        name="Dashboard"/>

    <menuitem 
        id="import_sale_dashboard_view" 
        action="gen_sale_import_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="2" 
        name="Import sale order"
        groups="sales_team.group_sale_salesman,sales_team.group_sale_manager"/>

    <menuitem 
        id="import_invoice_dashboard_view" 
        action="bi_gen_inv_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="3" 
        name="Import Invoice/Bills"/>

    <menuitem 
        id="import_payment_dashboard_view" 
        action="action_import_customer_payment"
        parent="import_all_in_one_dashboard_view"
        sequence="4" 
        name="Import Customer Payment"/>


    <menuitem 
        id="import_BOM_dashboard_view" 
        action="bi_gen_bom_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="5" 
        name="Import BOM"/>

    <menuitem 
        id="import_partner_dashboard_view" 
        action="action_gen_partner_import_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="6" 
        name="Import Partner"/>


    <menuitem 
        id="import_picking_dashboard_view" 
        action="bi_gen_picking_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="7" 
        name="Import Picking"/>


    <menuitem 
        id="import_pricelist_dashboard_view" 
        action="action_import_sale_pricelist_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="8" 
        name="Import Pricelist"/>
        
    <menuitem 
        id="import_variant_dashboard_view" 
        action="gen_product_variant_import_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="9" 
        name="Import Product"/>

    <menuitem 
        id="import_template_dashboard_view" 
        action="gen_product_import_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="10" 
        name="Import Product Variant"/>

    <menuitem 
        id="import_purchase_dashboard_view" 
        action="gen_pur_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="11" 
        name="Import Purchase"/>        

    <menuitem
        id="import_inventory_dashboard_view"
        action="gen_inv_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="12"
        name="Import Inventory"/>

    <menuitem
        id="import_inventory_dashboard_view"
        action="gen_inv_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="12"
        name="Import Inventory"/>
        
    <menuitem 
        id="import_project_task_dashboard_view" 
        action="import_task_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="13" 
        name="Import Task"/>

    <menuitem
        id="import_project_task_configuration"
        action="import_task_wizard_action"
        parent="project.menu_project_config"
        sequence="13"
        name="Import Task"/>
        
    <menuitem 
        id="import_product_image_dashboard_view" 
        action="action_import_product_image"
        parent="import_all_in_one_dashboard_view"
        sequence="14" 
        name="Import Product Image"/>
        
    <menuitem 
        id="gen_sale_wizard_import" 
        action="gen_sale_import_wizard_img"
        parent="import_all_in_one_dashboard_view"
        sequence="15" 
        name="Import Image From Zip File"/>
        
    <menuitem 
        id="import_attendance_dashboard_view" 
        action="hr_attendance_import_action"
        parent="import_all_in_one_dashboard_view"
        sequence="16" 
        name="Import Attendance"/>
        
    <menuitem 
        id="import_employee_timesheet_menu" 
        action="import_employee_timesheet_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="17" 
        name="Import Timesheets"/>
        
    <!--menuitem 
        id="import_pos_dashboard_view" 
        action="action_pos_import"
        parent="import_all_in_one_dashboard_view"
        sequence="18" 
        name="Import POS"/-->
        
    <menuitem 
        id="reordering_rule_import_menu" 
        action="reorder_rule_import_action"
        parent="import_all_in_one_dashboard_view"
        sequence="19" 
        name="Import Reordering Rules"/>
        
    <menuitem 
        id="stk_wizard_import" 
        action="stk_imp_wizard"
        parent="import_all_in_one_dashboard_view"
        sequence="20" 
        name="Import Lot/Serial Numbers"/>
        
    <menuitem 
        id="bi_gen_multiple_journal_wizard_import" 
        action="action_import_multiple_account_move_line"
        parent="import_all_in_one_dashboard_view"
        sequence="21" 
        name="Import Journal Entry"/>
        
    <menuitem 
        id="menu_lead_import" 
        action="lead_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="22" 
        name="Import Leads"/>
        
    <menuitem 
        id="menu_action_import_users_form" 
        action="import_users_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="23" 
        name="Import Users"/>
        
    <menuitem 
        id="menu_import_expense" 
        action="action_import_expense"
        parent="import_all_in_one_dashboard_view"
        sequence="25" 
        name="Import Employee Expenses"/>
        
   <menuitem 
        id="menu_import_lot_serial_picking" 
        action="lot_wizard_action"
        parent="import_all_in_one_dashboard_view"
        sequence="26" 
        name="Import Lot/Serial Picking"/>
</odoo>