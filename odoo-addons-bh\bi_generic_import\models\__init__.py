# -*- coding: utf-8 -*-
# Part of BrowseInfo. See LICENSE file for full copyright and licensing details.

from . import sale
from . import account_invoice
from . import purchase
from . import stock
from . import product
from . import partner
from . import picking
from . import payment
from . import import_order_lines
from . import import_po_lines
from . import import_invoice_lines
from . import bank_statement
from . import account_move
from . import supp_info
from . import pricelist
from . import product_variant
from . import import_dashboard
from . import task
from . import import_image
from . import attendances
from . import invoice_with_payment
from . import multiple_account_move
from . import stock_gen
from . import expense_import_wizard
from . import import_lot_serial_no
from . import stock_quant
from . import stock_move_line
from . import picking_type
from . import stock_move
from . import import_invoice_log
# vim:expandtab:smartindent:tabstop=4:softtabstop=4:shiftwidth=4: